# Ever Works CLI

The command-line interface for Ever Works - Open Directory Builder Platform.

## Installation

```bash
npm install -g ever-works-cli
```

## Usage

```bash
ever-works --help
```

Or use the short alias:

```bash
ew --help
```

## Commands

- `ever-works config` - Configure the CLI
- `ever-works directory` - Manage directories
- `ever-works serve` - Start local development server

For more information, visit: https://ever.works
